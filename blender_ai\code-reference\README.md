# Code Reference Library

**Comprehensive collection of reusable code snippets, patterns, and functions for Blender Python API**

## Overview

This library contains battle-tested code patterns extracted from successful AI-driven 3D modeling projects. Each snippet has been validated through real-world application and optimized for reliability and performance.

## Library Organization

### [Basic Patterns](basic-patterns.md)
- Scene management and setup
- Object creation and manipulation
- Safe operation patterns
- Error handling fundamentals

### [Advanced Patterns](advanced-patterns.md)
- BMesh operations and edit mode
- Modifier workflows
- Material and node systems
- Animation and keyframes

### [AI Integration](ai-integration.md)
- MCP tool integration patterns
- Visual feedback systems
- Validation and quality control
- Workflow automation

### [Utility Functions](utility-functions.md)
- Mathematical calculations
- Geometric validation
- Performance optimization
- Debug and logging tools

### [Professional Templates](professional-templates.md)
- Complete project setups
- Industry-standard workflows
- Quality assessment systems
- Production-ready patterns

## Quick Reference

### Essential Imports
```python
import bpy
import bmesh
import mathutils
from mathutils import Vector, Matrix, Euler
import math
import random
import os
```

### Standard Scene Setup
```python
def setup_standard_scene():
    """Create a standard scene with camera and lighting"""
    # Clear scene
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Add camera
    bpy.ops.object.camera_add(
        location=(7.36, -6.93, 4.96), 
        rotation=(1.11, 0.0, 0.62)
    )
    
    # Add lighting
    bpy.ops.object.light_add(
        type='SUN', 
        location=(4.08, 1.01, 5.9)
    )
    
    return True
```

### Safe Object Access
```python
def get_object_safely(obj_name):
    """Safely get object with error handling"""
    obj = bpy.data.objects.get(obj_name)
    if not obj:
        print(f"Warning: Object '{obj_name}' not found")
        return None
    return obj

def set_active_object(obj):
    """Safely set object as active"""
    if obj:
        bpy.context.view_layer.objects.active = obj
        obj.select_set(True)
        return True
    return False
```

### BMesh Template
```python
def bmesh_operation_template(obj, operation_func):
    """Standard BMesh operation template"""
    if not obj or obj.type != 'MESH':
        return False
    
    # Set active and enter edit mode
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Create BMesh instance
    bm = bmesh.from_edit_mesh(obj.data)
    bm.verts.ensure_lookup_table()
    bm.edges.ensure_lookup_table()
    bm.faces.ensure_lookup_table()
    
    try:
        # Perform operation
        result = operation_func(bm)
        
        # Update mesh
        bmesh.update_edit_mesh(obj.data)
        
        return result
        
    except Exception as e:
        print(f"BMesh operation failed: {e}")
        return False
        
    finally:
        # Always return to object mode
        bpy.ops.object.mode_set(mode='OBJECT')
```

## Code Categories

### 1. Scene Management
- Scene clearing and setup
- Camera positioning
- Lighting configuration
- Render settings

### 2. Object Operations
- Creation and deletion
- Transformation and positioning
- Selection and activation
- Property modification

### 3. Geometry Manipulation
- BMesh operations
- Modifier application
- Mesh editing
- Topology optimization

### 4. Material Systems
- Material creation
- Node network building
- Texture application
- Shader configuration

### 5. Animation
- Keyframe insertion
- Animation curves
- Timeline management
- Playback control

### 6. Validation and Quality
- Geometric validation
- Quality assessment
- Error detection
- Performance monitoring

### 7. AI Integration
- MCP tool interfaces
- Visual feedback processing
- Workflow automation
- Quality gates

## Usage Guidelines

### Code Reliability Standards
- **Error Handling**: All functions include comprehensive error handling
- **Input Validation**: Parameters are validated before use
- **Resource Cleanup**: Proper cleanup of temporary objects and data
- **Documentation**: Clear docstrings and inline comments

### Performance Considerations
- **Efficiency**: Optimized for common use cases
- **Memory Management**: Minimal memory footprint
- **Scalability**: Works with both simple and complex scenes
- **Caching**: Expensive operations are cached when possible

### Integration Patterns
- **Modularity**: Functions are designed for easy integration
- **Consistency**: Uniform naming and parameter conventions
- **Extensibility**: Easy to modify and extend for specific needs
- **Compatibility**: Works across different Blender versions

## Search and Navigation

### By Functionality
- **Scene Setup**: [Basic Patterns](basic-patterns.md#scene-setup)
- **Object Creation**: [Basic Patterns](basic-patterns.md#object-creation)
- **BMesh Operations**: [Advanced Patterns](advanced-patterns.md#bmesh-operations)
- **Material Creation**: [Advanced Patterns](advanced-patterns.md#materials)
- **AI Integration**: [AI Integration](ai-integration.md)

### By Complexity Level
- **Beginner**: Basic object and scene operations
- **Intermediate**: Modifiers, materials, and animation
- **Advanced**: BMesh, procedural systems, AI integration
- **Expert**: Custom tools and workflow automation

### By Use Case
- **Modeling**: Geometry creation and manipulation
- **Texturing**: Material and texture systems
- **Animation**: Keyframes and motion
- **Rendering**: Lighting and render setup
- **Automation**: Workflow and quality systems

## Contributing Guidelines

### Code Standards
```python
def function_template(param1, param2=None):
    """
    Brief description of function purpose.
    
    Args:
        param1 (type): Description of parameter
        param2 (type, optional): Description with default
    
    Returns:
        type: Description of return value
    
    Example:
        >>> result = function_template("example", param2="value")
        >>> print(result)
    """
    # Input validation
    if not param1:
        raise ValueError("param1 is required")
    
    try:
        # Main logic
        result = perform_operation(param1, param2)
        return result
        
    except Exception as e:
        print(f"Operation failed: {e}")
        return None
```

### Documentation Requirements
- Clear function purpose and usage
- Complete parameter descriptions
- Return value documentation
- Usage examples
- Error handling notes

### Testing Standards
- Validate with multiple object types
- Test error conditions
- Verify cleanup and resource management
- Performance testing for complex operations

## Version History

### v1.0 (Current)
- Initial library creation
- 50+ validated code patterns
- Complete documentation
- AI integration patterns

### Planned Updates
- Additional utility functions
- Performance optimizations
- Extended AI integration
- Community contributions

---

**Total Functions**: 50+  
**Validation Status**: All patterns tested in production  
**Compatibility**: Blender 3.0+  
**Last Updated**: July 9, 2025
