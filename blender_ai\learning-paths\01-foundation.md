# Foundation Level: Blender Python API Fundamentals

**Level**: Beginner  
**Prerequisites**: None  
**Estimated Duration**: 8-10 hours  
**Objective**: Master the core Blender Python API modules and basic 3D operations

## Learning Objectives

By completing this level, you will:
- ✅ Understand all core bpy modules and their purposes
- ✅ Execute basic 3D modeling operations programmatically
- ✅ Manage scenes, objects, and data structures
- ✅ Implement proper error handling and validation
- ✅ Follow established coding patterns and best practices

## Module 1: bpy.ops - Operations and Commands

**Duration**: 2 hours  
**Source Material**: `bpy_ops_egitimi.md`

### Core Concepts
- **Purpose**: Execute Blender operations programmatically
- **Pattern**: `bpy.ops.category.operation(parameters)`
- **Return Values**: Status sets (`{'FINISHED', 'CANCELLED', 'RUNNING_MODAL', 'PASS_THROUGH'}`)

### Essential Operations

#### Object Creation and Management
```python
# Scene clearing (always start clean)
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Basic primitive creation
bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
bpy.ops.mesh.primitive_uv_sphere_add(radius=1, segments=32, rings=16)
bpy.ops.mesh.primitive_cylinder_add(radius=1, depth=2, vertices=32)
```

#### Transform Operations
```python
# Object selection pattern
bpy.context.view_layer.objects.active = bpy.data.objects['ObjectName']
bpy.ops.object.select_all(action='DESELECT')
bpy.data.objects['ObjectName'].select_set(True)

# Transform operations
bpy.ops.transform.translate(value=(2, 0, 0))
bpy.ops.transform.rotate(value=0.785398, orient_axis='Z')  # 45 degrees
bpy.ops.transform.resize(value=(1.5, 1.5, 1.5))
```

### Key Learning Points
1. **Selection Management**: Always verify object selection before operations
2. **Parameter Validation**: Use correct units (radians for rotation, world units for distance)
3. **Error Handling**: Check return values and object existence
4. **Naming Conventions**: Blender may auto-rename objects (Cube.001, etc.)

### Practice Exercises
1. Create a scene with 5 different primitives at specific locations
2. Implement a function to safely select and transform objects
3. Create a grid of objects using transform operations
4. Handle object naming conflicts programmatically

## Module 2: bpy.data - Data Access and Manipulation

**Duration**: 2 hours  
**Source Material**: `bpy_data_egitimi.md`

### Core Concepts
- **Purpose**: Direct access to Blender's data structures
- **Advantage**: Faster than operators, no context requirements
- **Pattern**: `bpy.data.category['name']` or `bpy.data.category.new()`

### Essential Data Operations

#### Object Data Manipulation
```python
# Direct property access
obj = bpy.data.objects['Cube']
obj.location = (1, 2, 0)
obj.rotation_euler = (0, 0, 1.57)  # 90 degrees Z
obj.scale = (1.5, 1.5, 1.0)

# Safe object access
obj = bpy.data.objects.get('ObjectName')
if obj:
    # Perform operations
    pass
else:
    print("Object not found")
```

#### Material Creation and Assignment
```python
# Create new material
material = bpy.data.materials.new(name="MyMaterial")
material.use_nodes = True

# Get Principled BSDF node
bsdf = material.node_tree.nodes.get('Principled BSDF')
if bsdf:
    bsdf.inputs['Base Color'].default_value = (0.8, 0.2, 0.1, 1.0)
    bsdf.inputs['Roughness'].default_value = 0.3

# Assign to object
obj.data.materials.append(material)
```

### Key Learning Points
1. **Direct Access**: Faster than operators for simple property changes
2. **Safety Checks**: Always verify object existence before access
3. **Material Nodes**: Enable nodes before accessing node tree
4. **Data Relationships**: Understand object vs. mesh data distinction

### Practice Exercises
1. Create a function to safely access and modify object properties
2. Implement material creation with parameter validation
3. Build a scene setup function using only bpy.data operations
4. Create a data inspection tool to analyze scene contents

## Module 3: bpy.context - Context Management

**Duration**: 1.5 hours  
**Source Material**: `bpy_context_egitimi.md`

### Core Concepts
- **Purpose**: Access current Blender state and context
- **Read-Only**: Context values cannot be directly modified
- **Context Dependency**: Operations depend on current context state

### Essential Context Operations

#### Context Inspection
```python
# Check current state
print(f"Active object: {bpy.context.active_object.name if bpy.context.active_object else 'None'}")
print(f"Current mode: {bpy.context.mode}")
print(f"Selected objects: {[obj.name for obj in bpy.context.selected_objects]}")

# Scene information
scene = bpy.context.scene
print(f"Current frame: {scene.frame_current}")
print(f"Frame range: {scene.frame_start} - {scene.frame_end}")
```

#### Mode Management
```python
# Safe mode switching
if bpy.context.active_object and bpy.context.active_object.type == 'MESH':
    if bpy.context.mode != 'EDIT_MESH':
        bpy.ops.object.mode_set(mode='EDIT')
    # Perform edit operations
    bpy.ops.object.mode_set(mode='OBJECT')
```

### Key Learning Points
1. **Context Awareness**: Always check context before operations
2. **Mode Dependencies**: Some operations require specific modes
3. **Active Object**: Many operations depend on active object
4. **Context Override**: Advanced technique for operator context control

### Practice Exercises
1. Create a context inspection function
2. Implement safe mode switching with validation
3. Build a context-aware operation wrapper
4. Create a scene state backup and restore system

## Module 4: bpy.types - Type System Understanding

**Duration**: 1 hour  
**Source Material**: `bpy_types_egitimi.md`

### Core Concepts
- **Purpose**: Understand Blender's data type system
- **Type Inspection**: Analyze object properties and methods
- **Property Access**: Use bl_rna for dynamic property discovery

### Essential Type Operations

#### Type Inspection
```python
# Object type analysis
obj = bpy.data.objects['Cube']
print(f"Object type: {type(obj).__name__}")
print(f"Object data type: {type(obj.data).__name__}")

# Property discovery
properties = [prop.identifier for prop in obj.bl_rna.properties 
              if not prop.is_readonly]
print(f"Editable properties: {properties[:10]}")
```

#### Dynamic Property Access
```python
# Safe property access
def get_property_value(obj, prop_name):
    if hasattr(obj, prop_name):
        return getattr(obj, prop_name)
    return None

# Property validation
def validate_property_type(obj, prop_name, expected_type):
    prop = obj.bl_rna.properties.get(prop_name)
    return prop and prop.type == expected_type
```

### Key Learning Points
1. **Type Hierarchy**: Understand inheritance relationships
2. **Property Types**: Different property types have different access patterns
3. **Runtime Inspection**: Use bl_rna for dynamic analysis
4. **Type Safety**: Validate types before operations

### Practice Exercises
1. Create a type inspection utility
2. Build a property validation system
3. Implement dynamic property access functions
4. Create a type-safe operation wrapper

## Module 5: Scene Management and Basic Operations

**Duration**: 1.5 hours  
**Source Material**: Multiple sources integrated

### Core Concepts
- **Scene Setup**: Establish consistent working environments
- **Object Lifecycle**: Creation, modification, deletion patterns
- **Error Prevention**: Robust coding practices

### Essential Scene Operations

#### Standard Scene Setup
```python
def setup_basic_scene():
    """Create a standard scene with camera and lighting"""
    # Clear existing scene
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Add camera with optimal positioning
    bpy.ops.object.camera_add(
        location=(7.36, -6.93, 4.96), 
        rotation=(1.11, 0.0, 0.62)
    )
    
    # Add lighting
    bpy.ops.object.light_add(
        type='SUN', 
        location=(4.08, 1.01, 5.9)
    )
    
    # Set render settings
    bpy.context.scene.render.engine = 'CYCLES'
    bpy.context.scene.cycles.samples = 128
    
    return True
```

#### Object Management Patterns
```python
def safe_object_operation(obj_name, operation_func):
    """Safely perform operations on objects"""
    obj = bpy.data.objects.get(obj_name)
    if not obj:
        print(f"Object '{obj_name}' not found")
        return False
    
    try:
        # Set as active
        bpy.context.view_layer.objects.active = obj
        obj.select_set(True)
        
        # Perform operation
        result = operation_func(obj)
        return result
    except Exception as e:
        print(f"Operation failed: {e}")
        return False
```

### Key Learning Points
1. **Consistency**: Use standard scene setup patterns
2. **Error Handling**: Implement robust error checking
3. **Resource Management**: Clean up temporary objects
4. **Validation**: Verify operations completed successfully

### Practice Exercises
1. Create a comprehensive scene setup function
2. Implement object lifecycle management
3. Build error handling and recovery systems
4. Create a scene validation and testing framework

## Foundation Level Assessment

### Completion Criteria
- [ ] Successfully execute all module exercises
- [ ] Demonstrate understanding of each bpy module
- [ ] Create a working scene setup system
- [ ] Implement proper error handling patterns
- [ ] Show proficiency in object management

### Capstone Project: Automated Scene Creator
Create a system that:
1. Sets up a complete scene with camera and lighting
2. Creates multiple objects with different properties
3. Applies basic materials and transformations
4. Validates all operations and handles errors
5. Provides detailed logging and feedback

### Assessment Rubric
- **Basic (70%)**: All operations work with provided examples
- **Proficient (85%)**: Demonstrates understanding with modifications
- **Advanced (95%)**: Creates original solutions with best practices
- **Expert (100%)**: Implements robust, reusable systems

## Next Steps

Upon completion of Foundation Level:
1. **Review**: Ensure all concepts are understood
2. **Practice**: Complete additional exercises if needed
3. **Advance**: Proceed to [Intermediate Level](02-intermediate.md)
4. **Reference**: Use [Code Reference](../code-reference/) for ongoing support

---

**Estimated Completion Time**: 8-10 hours  
**Difficulty**: Beginner  
**Prerequisites**: None  
**Next Level**: [Intermediate Level](02-intermediate.md)
