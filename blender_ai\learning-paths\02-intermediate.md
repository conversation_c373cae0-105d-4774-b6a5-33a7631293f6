# Intermediate Level: Advanced Modeling and Material Systems

**Level**: Intermediate  
**Prerequisites**: [Foundation Level](01-foundation.md) completed  
**Estimated Duration**: 12-15 hours  
**Objective**: Master advanced modeling techniques, material systems, and professional workflows

## Learning Objectives

By completing this level, you will:
- ✅ Master BMesh operations for complex geometry manipulation
- ✅ Implement non-destructive modifier workflows
- ✅ Create realistic materials with node networks
- ✅ Understand rendering and lighting fundamentals
- ✅ Develop animation and keyframe systems
- ✅ Apply professional quality standards

## Module 6: BMesh Operations and Edit Mode

**Duration**: 3 hours  
**Source Material**: `bmesh_operatörleri_egitimi.md`, `bmesh_ve_edit_mode_deneyleri.md`

### Core Concepts
- **BMesh**: Low-level mesh manipulation system
- **Edit Mode**: Direct geometry component editing
- **Non-Destructive**: Preserve original geometry when possible

### Essential BMesh Operations

#### BMesh Workflow Pattern
```python
import bmesh

def bmesh_operation_template(obj, operation_func):
    """Standard BMesh operation template"""
    # Enter Edit Mode
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Create BMesh instance
    bm = bmesh.from_edit_mesh(obj.data)
    
    # Ensure lookup tables are updated
    bm.verts.ensure_lookup_table()
    bm.edges.ensure_lookup_table()
    bm.faces.ensure_lookup_table()
    
    try:
        # Perform operation
        result = operation_func(bm)
        
        # Update mesh
        bmesh.update_edit_mesh(obj.data)
        
    finally:
        # Return to Object Mode
        bpy.ops.object.mode_set(mode='OBJECT')
    
    return result
```

#### Geometry Manipulation
```python
def subdivide_selected_edges(bm, cuts=1):
    """Subdivide selected edges"""
    selected_edges = [e for e in bm.edges if e.select]
    if selected_edges:
        bmesh.ops.subdivide_edges(
            bm, 
            edges=selected_edges, 
            cuts=cuts, 
            use_grid_fill=True
        )
    return len(selected_edges)

def extrude_face_region(bm, face_indices, move_vector):
    """Extrude faces and move them"""
    faces = [bm.faces[i] for i in face_indices if i < len(bm.faces)]
    
    ret = bmesh.ops.extrude_face_region(bm, geom=faces)
    
    # Move extruded vertices
    verts = [ele for ele in ret['geom'] if isinstance(ele, bmesh.types.BMVert)]
    for v in verts:
        v.co += move_vector
    
    return len(verts)
```

### Advanced BMesh Techniques

#### Face Selection and Manipulation
```python
def select_faces_by_normal(bm, direction_vector, threshold=0.5):
    """Select faces based on normal direction"""
    selected_count = 0
    for face in bm.faces:
        if face.normal.dot(direction_vector) > threshold:
            face.select = True
            selected_count += 1
    return selected_count

def delete_selected_geometry(bm, context='FACES'):
    """Delete selected geometry"""
    selected_geom = []
    
    if context == 'FACES':
        selected_geom = [f for f in bm.faces if f.select]
    elif context == 'EDGES':
        selected_geom = [e for e in bm.edges if e.select]
    elif context == 'VERTS':
        selected_geom = [v for v in bm.verts if v.select]
    
    if selected_geom:
        bmesh.ops.delete(bm, geom=selected_geom, context=context)
    
    return len(selected_geom)
```

### Key Learning Points
1. **Lookup Tables**: Always call ensure_lookup_table() before accessing elements
2. **Mode Management**: Proper Edit/Object mode transitions
3. **Error Handling**: Use try/finally for mode cleanup
4. **Selection Management**: Clear and set selections explicitly

### Practice Exercises
1. Create a function to add detail to specific faces
2. Implement a geometry cleanup tool
3. Build a procedural modeling system using BMesh
4. Create a mesh analysis and validation tool

## Module 7: Modifier Systems and Non-Destructive Workflows

**Duration**: 2.5 hours  
**Source Material**: `modifierlar_ve_animasyon_deneyleri.md`

### Core Concepts
- **Non-Destructive**: Preserve original geometry
- **Modifier Stack**: Order matters for complex effects
- **Real-time Preview**: Immediate visual feedback

### Essential Modifier Operations

#### Subdivision Surface Modifier
```python
def add_subdivision_surface(obj, levels=2, render_levels=None):
    """Add subdivision surface modifier"""
    if render_levels is None:
        render_levels = levels
    
    modifier = obj.modifiers.new(name="Subdivision", type='SUBSURF')
    modifier.levels = levels
    modifier.render_levels = render_levels
    
    return modifier

# Usage example
obj = bpy.data.objects['Cube']
subsurf = add_subdivision_surface(obj, levels=2, render_levels=3)
```

#### Array Modifier
```python
def add_array_modifier(obj, count=3, offset=(2.0, 0.0, 0.0)):
    """Add array modifier for object duplication"""
    modifier = obj.modifiers.new(name="Array", type='ARRAY')
    modifier.count = count
    modifier.relative_offset_displace = offset
    
    return modifier

# Create a row of objects
array_mod = add_array_modifier(obj, count=5, offset=(2.5, 0.0, 0.0))
```

#### Mirror Modifier
```python
def add_mirror_modifier(obj, axis='X', use_bisect=True):
    """Add mirror modifier for symmetrical modeling"""
    modifier = obj.modifiers.new(name="Mirror", type='MIRROR')
    
    # Set axis
    modifier.use_axis[0] = (axis == 'X')  # X-axis
    modifier.use_axis[1] = (axis == 'Y')  # Y-axis
    modifier.use_axis[2] = (axis == 'Z')  # Z-axis
    
    modifier.use_bisect_axis[0] = use_bisect and (axis == 'X')
    modifier.use_bisect_axis[1] = use_bisect and (axis == 'Y')
    modifier.use_bisect_axis[2] = use_bisect and (axis == 'Z')
    
    return modifier
```

### Advanced Modifier Techniques

#### Modifier Stack Management
```python
def reorder_modifier(obj, modifier_name, new_index):
    """Reorder modifier in the stack"""
    modifier = obj.modifiers.get(modifier_name)
    if not modifier:
        return False
    
    # Move modifier to new position
    current_index = list(obj.modifiers).index(modifier)
    if current_index != new_index:
        # Blender doesn't have direct reordering, so we recreate
        # This is a simplified approach
        pass
    
    return True

def apply_modifier(obj, modifier_name):
    """Apply modifier to mesh"""
    modifier = obj.modifiers.get(modifier_name)
    if modifier:
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.modifier_apply(modifier=modifier_name)
        return True
    return False
```

### Key Learning Points
1. **Order Matters**: Modifier stack order affects final result
2. **Performance**: Too many modifiers can slow viewport
3. **Non-Destructive**: Keep original geometry intact
4. **Preview vs Render**: Different quality settings for efficiency

### Practice Exercises
1. Create a modifier management system
2. Build complex objects using modifier combinations
3. Implement modifier presets for common workflows
4. Create a modifier optimization tool

## Module 8: Material Systems and Node Networks

**Duration**: 3 hours  
**Source Material**: `malzemeler_ve_render_deneyleri.md`

### Core Concepts
- **Node-Based**: Materials use node networks
- **Principled BSDF**: Standard physically-based shader
- **Texture Mapping**: UV coordinates and procedural textures

### Essential Material Operations

#### Basic Material Creation
```python
def create_basic_material(name, base_color=(0.8, 0.8, 0.8, 1.0)):
    """Create a basic material with Principled BSDF"""
    material = bpy.data.materials.new(name=name)
    material.use_nodes = True
    
    # Get Principled BSDF node
    bsdf = material.node_tree.nodes.get('Principled BSDF')
    if bsdf:
        bsdf.inputs['Base Color'].default_value = base_color
    
    return material

def assign_material_to_object(obj, material):
    """Assign material to object"""
    if not obj.data.materials:
        obj.data.materials.append(material)
    else:
        obj.data.materials[0] = material
```

#### Advanced Material Properties
```python
def create_metal_material(name, base_color, roughness=0.1):
    """Create a metallic material"""
    material = create_basic_material(name, base_color)
    bsdf = material.node_tree.nodes.get('Principled BSDF')
    
    if bsdf:
        bsdf.inputs['Metallic'].default_value = 1.0
        bsdf.inputs['Roughness'].default_value = roughness
        bsdf.inputs['Specular'].default_value = 1.0
    
    return material

def create_glass_material(name, color=(1.0, 1.0, 1.0, 1.0), ior=1.45):
    """Create a glass material"""
    material = create_basic_material(name, color)
    bsdf = material.node_tree.nodes.get('Principled BSDF')
    
    if bsdf:
        bsdf.inputs['Transmission'].default_value = 1.0
        bsdf.inputs['IOR'].default_value = ior
        bsdf.inputs['Roughness'].default_value = 0.0
        bsdf.inputs['Alpha'].default_value = 0.1
    
    # Set blend mode
    material.blend_method = 'BLEND'
    
    return material
```

#### Node Network Creation
```python
def add_noise_texture_to_material(material, scale=5.0, detail=2.0):
    """Add noise texture to material roughness"""
    nodes = material.node_tree.nodes
    links = material.node_tree.links
    
    # Create noise texture node
    noise = nodes.new(type='ShaderNodeTexNoise')
    noise.inputs['Scale'].default_value = scale
    noise.inputs['Detail'].default_value = detail
    
    # Get Principled BSDF
    bsdf = nodes.get('Principled BSDF')
    
    if bsdf:
        # Connect noise to roughness
        links.new(noise.outputs['Fac'], bsdf.inputs['Roughness'])
    
    return noise
```

### Key Learning Points
1. **Node Networks**: Materials are built with connected nodes
2. **Physically Based**: Use realistic material properties
3. **Texture Coordinates**: Understand UV mapping and procedural textures
4. **Performance**: Complex node networks affect render time

### Practice Exercises
1. Create a material library with common types
2. Build complex node networks for specific effects
3. Implement procedural texture systems
4. Create material validation and optimization tools

## Module 9: Rendering and Lighting Fundamentals

**Duration**: 2 hours  
**Source Material**: Multiple sources integrated

### Core Concepts
- **Render Engines**: Cycles vs Eevee characteristics
- **Lighting Setup**: Three-point lighting and HDRI
- **Camera Positioning**: Composition and framing

### Essential Rendering Operations

#### Render Engine Configuration
```python
def setup_cycles_rendering(samples=128, use_denoising=True):
    """Configure Cycles render engine"""
    scene = bpy.context.scene
    scene.render.engine = 'CYCLES'
    scene.cycles.samples = samples
    scene.cycles.use_denoising = use_denoising
    
    # Set resolution
    scene.render.resolution_x = 1920
    scene.render.resolution_y = 1080
    scene.render.resolution_percentage = 100
    
    return True

def setup_eevee_rendering(samples=64, use_bloom=True):
    """Configure Eevee render engine"""
    scene = bpy.context.scene
    scene.render.engine = 'BLENDER_EEVEE'
    scene.eevee.taa_render_samples = samples
    scene.eevee.use_bloom = use_bloom
    
    return True
```

#### Lighting Setup
```python
def create_three_point_lighting():
    """Create professional three-point lighting setup"""
    # Key light (main light)
    bpy.ops.object.light_add(type='SUN', location=(3.0, -3.0, 4.0))
    key_light = bpy.context.active_object
    key_light.data.energy = 5.0
    key_light.data.color = (1.0, 0.95, 0.9)  # Warm white
    key_light.name = "Key_Light"
    
    # Fill light (soften shadows)
    bpy.ops.object.light_add(type='AREA', location=(-2.5, 1.0, 2.0))
    fill_light = bpy.context.active_object
    fill_light.data.energy = 2.0
    fill_light.data.color = (0.9, 0.95, 1.0)  # Cool white
    fill_light.name = "Fill_Light"
    
    # Rim light (edge definition)
    bpy.ops.object.light_add(type='SPOT', location=(0.0, 3.0, 2.5))
    rim_light = bpy.context.active_object
    rim_light.data.energy = 3.0
    rim_light.data.spot_size = 1.2
    rim_light.name = "Rim_Light"
    
    return [key_light, fill_light, rim_light]
```

#### Camera Setup
```python
def setup_camera_for_object(target_obj, distance=5.0, height=3.0):
    """Position camera to frame object optimally"""
    # Calculate camera position
    target_location = target_obj.location
    camera_location = (
        target_location.x + distance * 0.7,
        target_location.y - distance * 0.7,
        target_location.z + height
    )
    
    # Create or move camera
    camera = bpy.data.objects.get('Camera')
    if not camera:
        bpy.ops.object.camera_add(location=camera_location)
        camera = bpy.context.active_object
    else:
        camera.location = camera_location
    
    # Point camera at target
    direction = target_location - camera.location
    camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
    
    return camera
```

### Key Learning Points
1. **Engine Choice**: Cycles for quality, Eevee for speed
2. **Lighting Quality**: Good lighting makes average models look great
3. **Camera Composition**: Follow photography principles
4. **Render Optimization**: Balance quality vs render time

### Practice Exercises
1. Create render preset systems
2. Build automated lighting setups
3. Implement camera positioning algorithms
4. Create render quality assessment tools

## Module 10: Animation and Keyframe Systems

**Duration**: 1.5 hours  
**Source Material**: `modifierlar_ve_animasyon_deneyleri.md`

### Core Concepts
- **Keyframes**: Define object states at specific times
- **Interpolation**: Blender calculates between keyframes
- **Animation Curves**: Control timing and easing

### Essential Animation Operations

#### Basic Keyframe Animation
```python
def create_location_animation(obj, start_frame, end_frame, start_pos, end_pos):
    """Create simple location animation"""
    # Set start keyframe
    bpy.context.scene.frame_set(start_frame)
    obj.location = start_pos
    obj.keyframe_insert(data_path="location", frame=start_frame)
    
    # Set end keyframe
    bpy.context.scene.frame_set(end_frame)
    obj.location = end_pos
    obj.keyframe_insert(data_path="location", frame=end_frame)
    
    return True

def create_rotation_animation(obj, start_frame, end_frame, rotations):
    """Create rotation animation (full 360 degree turn)"""
    bpy.context.scene.frame_set(start_frame)
    obj.rotation_euler = (0, 0, 0)
    obj.keyframe_insert(data_path="rotation_euler", frame=start_frame)
    
    bpy.context.scene.frame_set(end_frame)
    obj.rotation_euler = (0, 0, rotations * 6.28318)  # 2π * rotations
    obj.keyframe_insert(data_path="rotation_euler", frame=end_frame)
    
    return True
```

#### Complex Animation Systems
```python
def create_multi_property_animation(obj, keyframes_data):
    """Create animation with multiple properties
    
    keyframes_data format:
    [
        {'frame': 1, 'location': (0,0,0), 'rotation': (0,0,0), 'scale': (1,1,1)},
        {'frame': 50, 'location': (5,0,0), 'rotation': (0,0,1.57), 'scale': (2,2,2)},
    ]
    """
    for keyframe in keyframes_data:
        frame = keyframe['frame']
        bpy.context.scene.frame_set(frame)
        
        if 'location' in keyframe:
            obj.location = keyframe['location']
            obj.keyframe_insert(data_path="location", frame=frame)
        
        if 'rotation' in keyframe:
            obj.rotation_euler = keyframe['rotation']
            obj.keyframe_insert(data_path="rotation_euler", frame=frame)
        
        if 'scale' in keyframe:
            obj.scale = keyframe['scale']
            obj.keyframe_insert(data_path="scale", frame=frame)
    
    return True
```

### Key Learning Points
1. **Frame Management**: Always set current frame before keyframing
2. **Data Paths**: Use correct property names for keyframe_insert
3. **Interpolation**: Understand linear vs bezier interpolation
4. **Performance**: Too many keyframes can slow playback

### Practice Exercises
1. Create an animation system with easing curves
2. Build procedural animation generators
3. Implement animation validation and optimization
4. Create animation preset libraries

## Intermediate Level Assessment

### Completion Criteria
- [ ] Master BMesh operations for geometry manipulation
- [ ] Implement modifier workflows effectively
- [ ] Create realistic materials with node networks
- [ ] Set up professional lighting and rendering
- [ ] Develop animation systems with keyframes

### Capstone Project: Realistic Product Visualization
Create a complete product visualization system that:
1. Models a complex object using BMesh operations
2. Applies non-destructive modifiers for detail
3. Creates realistic materials with node networks
4. Sets up professional lighting and camera work
5. Renders high-quality final images
6. Includes subtle animation for presentation

### Assessment Rubric
- **Basic (70%)**: All techniques work with provided examples
- **Proficient (85%)**: Demonstrates creative application of techniques
- **Advanced (95%)**: Creates original, professional-quality work
- **Expert (100%)**: Implements innovative solutions and optimizations

## Next Steps

Upon completion of Intermediate Level:
1. **Portfolio**: Create examples showcasing all learned techniques
2. **Optimization**: Review and optimize your workflow patterns
3. **Advance**: Proceed to [Advanced Level](03-advanced.md)
4. **Specialize**: Focus on areas of particular interest

---

**Estimated Completion Time**: 12-15 hours  
**Difficulty**: Intermediate  
**Prerequisites**: [Foundation Level](01-foundation.md)  
**Next Level**: [Advanced Level](03-advanced.md)
