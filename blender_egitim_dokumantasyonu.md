# Blender Eğitim Dokümantasyonu

Bu dokümantasyon, <PERSON><PERSON><PERSON> yazılımını öğrenme sürecinde modelleme temellerine odaklanarak, <PERSON>lender MCP (Model Context Protocol) ve Blender Python API'si (bpy) ile nasıl çalışılacağını açıklamaktadır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blender ile ilgili temel bilgiler, modelleme teknikleri, MCP kullanımı ve Python API'si ile otomasyon örnekleri adım adım sunulmaktadır.

## 1. Blender Modelleme Temelleri

Blender, 3D modelleme, animasyon, render ve daha birçok alanda kullanılan açık kaynaklı bir yazılımdır. Modelleme temelleri, Blender arayüzüne aşinalık kazanmak ve temel araçları kullanmayı öğrenmekle başlar.

### 1.1 Blender Arayüzüne Giriş
- **<PERSON>ç <PERSON> ve Paneller:** <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> men<PERSON>ğ<PERSON>, solda araç <PERSON> (Toolshelf), sağda özellikler paneli (Properties) ve ortada 3D görünüm alanından oluşur.
- **Kısayollar:** Blender, hızlı çalışma için birçok kısayol sunar. Örneğin, `G` tuşu ile nesneleri taşıyabilir, `R` ile döndürebilir ve `S` ile ölçeklendirebilirsiniz.
- **Navigasyon:** Fare orta tuşu ile 3D alanda dönme, Shift + orta tuş ile kaydırma ve Ctrl + orta tuş ile yakınlaştırma/uzaklaştırma yapılabilir.

### 1.2 Temel Modelleme Teknikleri
- **Vertex, Edge, Face Manipülasyonu:** Blender'da modelleme, nesnelerin temel bileşenleri olan vertex (nokta), edge (kenar) ve face (yüzey) üzerinden yapılır. `Tab` tuşu ile Edit Mode'a geçerek bu bileşenleri düzenleyebilirsiniz.
- **Extrude (E):** Seçili bir yüzeyi veya kenarı dışarı doğru uzatmak için kullanılır. Örneğin, bir küpün yüzeyini seçip `E` tuşuna basarak yeni bir geometri oluşturabilirsiniz.
- **Loop Cut (Ctrl + R):** Bir nesneye yeni kenar döngüleri eklemek için kullanılır. Bu, modelin detayını artırmaya yardımcı olur.
- **Modifier Kullanımı:** Modifier'lar, nesnelere otomatik olarak uygulanan efektlerdir. Örneğin, `Mirror Modifier` ile simetrik modeller oluşturabilir, `Subdivision Surface` ile yüzeyleri pürüzsüzleştirebilirsiniz.

### 1.3 Basit Bir 3D Model Oluşturma
Basit bir masa modeli oluşturmak için şu adımları izleyebilirsiniz:
1. Blender'ı açın ve varsayılan küpü seçin (`Shift + A` ile yeni nesne ekleyebilirsiniz).
2. `Tab` tuşu ile Edit Mode'a geçin.
3. `Ctrl + R` ile loop cut ekleyerek masa ayakları için bölümler oluşturun.
4. `E` tuşu ile extrude yaparak ayakları aşağı doğru uzatın.
5. `S` tuşu ile ölçeklendirerek ayakların kalınlığını ayarlayın.
6. Properties panelinden malzemeler ekleyerek masaya renk veya doku uygulayın.

Daha fazla bilgi için şu kaynaklara göz atabilirsiniz:
- [Blender Guru Tutorials](https://www.blenderguru.com/tutorials/category/Modeling)
- [CG Cookie Beginner's Guide](https://cgcookie.com/posts/beginner-s-guide-to-3d-modeling-in-blender)

## 2. Blender MCP (Model Context Protocol) Kullanımı

Blender MCP, Blender'ı Claude AI gibi yapay zeka araçlarıyla entegre etmek için kullanılan bir protokoldür. Bu entegrasyon, doğal dil komutlarıyla 3D modelleme ve sahne manipülasyonu yapmayı mümkün kılar.

### 2.1 Blender MCP Nedir ve Nasıl Çalışır?
- Blender MCP, Blender ile Claude AI arasında bir köprü görevi görür. Model Context Protocol (MCP) üzerinden çalışarak, Blender'ın içindeki işlemleri otomatikleştirmek için komutlar gönderir.
- MCP araçları, sahne bilgisi alma (`get_scene_info`), nesne bilgisi alma (`get_object_info`), Python kodu çalıştırma (`execute_blender_code`) ve çeşitli entegrasyonlar (Polyhaven, Sketchfab, Hyper3D) gibi işlevler sunar.

### 2.2 Blender MCP Kurulumu ve Yapılandırması
- **Kurulum:** Blender MCP, genellikle bir eklenti veya harici bir araç olarak kurulur. `uvx blender-mcp` komutu ile çalıştırılabilir. Detaylı kurulum talimatları için [blender-mcp.com](https://blender-mcp.com/) adresine bakabilirsiniz.
- **Yapılandırma:** MCP'nin çalışması için Blender'ın açık olması ve MCP sunucusunun doğru şekilde bağlanması gerekir. VSCode ile entegrasyon için Roo Code veya OpenRouter API kurulumu gerekebilir.
- **Kullanım Örneği:** MCP ile bir nesne oluşturmak için `execute_blender_code` aracı kullanılabilir. Örneğin, bir küp eklemek için şu kod çalıştırılabilir:
  ```python
  import bpy
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  ```

### 2.3 Dikkat Edilmesi Gerekenler
- **Adım Adım Kod Çalıştırma:** MCP ile kod çalıştırırken, karmaşık işlemleri küçük parçalara bölerek çalıştırmak önemlidir. Bu, hata ayıklamayı kolaylaştırır.
- **Bağlantı Sorunları:** Blender MCP'nin çalışması için internet bağlantısı ve doğru API anahtarları gerekebilir. Bağlantı sorunları durumunda, yapılandırmayı kontrol edin.
- **Performans:** Büyük sahnelerde veya karmaşık kodlarda performans düşüşleri yaşanabilir. Bu durumda, işlemleri basitleştirmek veya sahneyi optimize etmek gerekebilir.

Daha fazla bilgi için şu kaynaklara göz atabilirsiniz:
- [Blender MCP Tutorials](https://blender-mcp.com/tutorials.html)
- [GitHub - ahujasid/blender-mcp](https://github.com/ahujasid/blender-mcp)

## 3. Blender Python API'si (bpy) ile Çalışma

Blender Python API'si (bpy), Blender içinde Python kodları yazarak otomasyon ve özelleştirme yapmayı sağlar. Bu API, modelleme, animasyon, render ve daha birçok işlemi programatik olarak kontrol etmeye olanak tanır.

### 3.1 Temel bpy Komutları
- **Nesne Oluşturma:** Yeni bir nesne eklemek için `bpy.ops.mesh.primitive_*_add()` fonksiyonları kullanılır. Örneğin, bir küre eklemek için:
  ```python
  import bpy
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  ```
- **Nesne Dönüştürme:** Nesnelerin konumunu, dönüşünü ve ölçeğini değiştirmek için `bpy.data.objects` kullanılır. Örneğin, bir nesneyi taşımak için:
  ```python
  import bpy
  obj = bpy.data.objects["Cube"]
  obj.location = (2, 3, 1)
  ```
- **Malzeme Atama:** Nesnelere malzeme eklemek için `bpy.data.materials` kullanılır. Örneğin, yeni bir malzeme oluşturup atamak için:
  ```python
  import bpy
  mat = bpy.data.materials.new(name="New_Material")
  mat.diffuse_color = (1, 0, 0, 1)  # Kırmızı renk
  obj = bpy.data.objects["Cube"]
  obj.data.materials.append(mat)
  ```

### 3.2 Modelleme Otomasyonu
Blender Python API'si ile modelleme işlemlerini otomatikleştirebilirsiniz. Örneğin, bir dizi küp oluşturmak için bir döngü kullanabilirsiniz:
```python
import bpy
for i in range(5):
    bpy.ops.mesh.primitive_cube_add(size=1, location=(i*2, 0, 0))
```

Daha fazla bilgi için şu kaynaklara göz atabilirsiniz:
- [Blender Python API Quickstart](https://docs.blender.org/api/current/info_quickstart.html)
- [Blender Python API Introduction](https://docs.blender.org/manual/en/latest/advanced/scripting/introduction.html)

## 4. Donut Tutorialı: Adım Adım Gerçekçi Modelleme

Kullanıcı talebi üzerine, Blender Guru'nun ünlü "Donut Tutorial" serisine benzer bir şekilde, temel bir donut modelini adım adım tasarladım. Bu süreç, modelleme temellerini öğrenme ve Blender MCP ile Python kodları kullanma becerilerimi geliştirmeme yardımcı oldu. Aşağıda, bu tutorialın adımları ve deneyimlerim yer alıyor.

### 4.1 Adım 1: Sahneyi Temizleme
- **Amaç:** Mevcut nesneleri kaldırarak temiz bir çalışma alanı oluşturmak.
- **Kod:** `bpy.ops.object.select_all(action='SELECT'); bpy.ops.object.delete(use_global=False)`
- **Deneyim:** Sahneyi temizlemek, yeni bir projeye başlamadan önce gereksiz karmaşayı önlüyor. Ancak, kamera ve ışık gibi varsayılan nesnelerin de silindiğini fark ettim, bu yüzden onları tekrar eklemem gerekti.

### 4.2 Adım 2: Kamera ve Işık Ekleme
- **Amaç:** Render için gerekli olan kamera ve ışık kaynağını eklemek.
- **Kod:** `bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62)); bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))`
- **Deneyim:** Kamera ve ışığın konumunu doğru ayarlamak, render sonucunu doğrudan etkiliyor. Varsayılan değerler yerine, daha iyi bir açı ve aydınlatma için bu değerleri manuel olarak belirledim.

### 4.3 Adım 3: Donut Şeklini Oluşturma (Torus Ekleme)
- **Amaç:** Donutun temel şekli için bir torus nesnesi eklemek.
- **Kod:** `bpy.ops.mesh.primitive_torus_add(location=(0, 0, 0), major_radius=1, minor_radius=0.4, major_segments=48, minor_segments=16)`
- **Deneyim:** Torus nesnesi, donutun halka şeklini oluşturmak için ideal. Segment sayılarını artırarak daha pürüzsüz bir yüzey elde ettim.

### 4.4 Adım 4: Yüzeyi Pürüzsüzleştirme (Subdivision Surface Modifier)
- **Amaç:** Donutun yüzeyini daha gerçekçi hale getirmek için pürüzsüzleştirme uygulamak.
- **Kod:** `bpy.context.view_layer.objects.active = bpy.data.objects['Torus']; bpy.ops.object.modifier_add(type='SUBSURF'); modifier = bpy.data.objects['Torus'].modifiers['Subdivision']; modifier.levels = 2; modifier.render_levels = 2`
- **Deneyim:** Subdivision Surface modifier'ı, düşük poligonlu bir modeli pürüzsüzleştirerek daha organik bir görünüm sağlıyor. Seviyeleri 2 olarak ayarlamak, performans ve görünüm arasında iyi bir denge kurdu.

### 4.5 Adım 5: Glaze Malzemesi Ekleme
- **Amaç:** Donutun üzerine parlak, gerçekçi bir glaze (sır) etkisi eklemek.
- **Kod:** `mat = bpy.data.materials.new(name='Donut_Glaze'); mat.use_nodes = True; bsdf = mat.node_tree.nodes.get('Principled BSDF'); bsdf.inputs['Base Color'].default_value = (0.6, 0.3, 0.1, 1.0); bsdf.inputs['Roughness'].default_value = 0.2; bsdf.inputs['Metallic'].default_value = 0.0; obj = bpy.data.objects['Torus']; obj.data.materials.append(mat)`
- **Deneyim:** Principled BSDF shader'ı, gerçekçi malzemeler oluşturmak için çok yönlü bir araç. Kahverengi ton ve düşük roughness değeri, donutun glaze kaplamasını parlak ve iştah açıcı gösterdi.

### 4.6 Adım 6: Tabak Ekleme
- **Amaç:** Donutun altında bir tabak oluşturmak için düzlem eklemek.
- **Kod:** `bpy.ops.mesh.primitive_plane_add(size=5, location=(0, 0, -0.5)); plane = bpy.context.active_object; plane.name = 'Plate'; mat = bpy.data.materials.new(name='Plate_Material'); mat.use_nodes = True; bsdf = mat.node_tree.nodes.get('Principled BSDF'); bsdf.inputs['Base Color'].default_value = (0.8, 0.8, 0.8, 1.0); bsdf.inputs['Roughness'].default_value = 0.5; bsdf.inputs['Metallic'].default_value = 0.1; plane.data.materials.append(mat)`
- **Deneyim:** Basit bir düzlem, tabak olarak kullanılabilir. Beyaz seramik malzemesi, donutun renklerini öne çıkararak kontrast sağladı.

### 4.7 Adım 7: Render Ayarları
- **Amaç:** Gerçekçi bir render için Cycles motorunu ve dünya ayarlarını yapılandırmak.
- **Kod:** `bpy.context.scene.render.engine = 'CYCLES'; bpy.context.scene.cycles.samples = 128; bpy.context.scene.cycles.use_denoising = True; bpy.context.scene.world.use_nodes = True; bg = bpy.context.scene.world.node_tree.nodes.get('Background'); bg.inputs['Color'].default_value = (0.7, 0.8, 1.0, 1.0); bg.inputs['Strength'].default_value = 1.0`
- **Deneyim:** Cycles render motoru, fiziksel tabanlı aydınlatma ile daha gerçekçi sonuçlar veriyor. Denoising özelliği, render süresini optimize ederken gürültüyü azalttı. Açık mavi arka plan, sahneye ferah bir hava kattı.

### 4.8 Genel Deneyim ve Öğrenimler
Bu donut tutorialı, Blender MCP ile Python kodları kullanarak modelleme sürecini otomatikleştirmeyi öğrenmeme yardımcı oldu. Her adımı küçük parçalara bölerek çalıştırmak, hata yapma riskini azalttı ve süreci daha yönetilebilir hale getirdi. Ayrıca, malzemeler ve render ayarları gibi görsel unsurların, modelin son görünümünde ne kadar büyük bir fark yarattığını fark ettim. Blender Python API'si, tekrarlayan görevleri otomatikleştirmek için güçlü bir araç, ancak kodun doğru çalıştığından emin olmak için her adım sonrası sahneyi kontrol etmek önemli.

## 5. Notlar ve Öneriler

- **Pratik Yapma:** Modelleme becerilerinizi geliştirmek için basit projelerle başlayın (örneğin, bir bardak veya sandalye modellemek). Her yeni teknik öğrendiğinizde, bunu bir projede uygulayın.
- **Hata Ayıklama:** Python kodlarında hata alırsanız, Blender'ın Console penceresini kontrol edin. Hataların çoğu burada detaylı olarak açıklanır.
- **Topluluk Desteği:** Blender topluluğu çok aktiftir. Sorularınız için [Blender Stack Exchange](https://blender.stackexchange.com/) veya [Blender Artists Forum](https://blenderartists.org/) gibi platformları kullanabilirsiniz.
- **Kaynakları Güncel Tutma:** Blender sık sık güncellenir, bu yüzden kullandığınız kaynakların Blender sürümünüzle uyumlu olduğundan emin olun.

Bu dokümantasyon, Blender öğrenme sürecinizde bir rehber olarak kullanılabilir. İlerledikçe, karşılaştığınız zorlukları ve çözümleri not alarak bu dokümanı genişletebilirsiniz.
