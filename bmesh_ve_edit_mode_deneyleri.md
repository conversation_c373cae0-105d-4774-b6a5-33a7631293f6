# Blender Python API: B<PERSON>esh ve Edit Mode Deneyleri

Bu <PERSON>, Blender Python API'sinde bulunan `b<PERSON>h` mod<PERSON>lünün ve Edit Mode'un kullanımını test etme ve deneyimleme sürecini detaylı bir şekilde açıklamaktadır. `b<PERSON><PERSON>`, <PERSON>lender'ın mesh verilerini manipüle etmek için kullanılan güçlü bir modüldür ve Edit Mode, nesnelerin temel bileşenlerini (vertex, edge, face) doğrudan düzenlemeye olanak tanır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, `bmesh` ve Edit Mode ile ilgili temel işlevler, kullanım örnekleri ve bu süreçteki deneyimlerim yer almaktadır.

## 1. BMesh ve Edit Mode'a Giriş

`bmesh` modülü, Blender'ın mesh verilerine düşük seviyede erişim sağlar ve vertex, edge, face gibi geometrik bileşenleri manipüle etmeye olanak tanır. Edit Mode ise, nesnelerin bu bileşenlerini doğrudan seçip düzenleyebileceğiniz bir çalışma modudur. Bu modül ve mod, detaylı modelleme işlemleri için temel araçlar sunar; örneğin, bir nesneyi bir bütün olarak değil, onu oluşturan parçacıkları şekillendirerek özelleştirebilirsiniz.

Daha fazla bilgi için şu kaynaklara göz atabilirsiniz:
- [BMesh Module (bmesh)](https://docs.blender.org/api/current/bmesh.html)

## 2. Test Ortamının Hazırlanması

`bmesh` ve Edit Mode'u test etmek için basit bir küp nesnesi üzerinde çalışıyorum. İlk olarak, sahneyi temizleyip kamera, ışık ve test için bir küp ekliyorum. Bu, manipülasyonlar için temiz bir başlangıç noktası sağlıyor.

- **Kod:** 
  ```python
  import bpy
  # Mevcut nesneleri temizle
  bpy.ops.object.select_all(action='SELECT')
  bpy.ops.object.delete(use_global=False)
  # Kamera ekle
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Işık ekle
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Test için bir küp ekle
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  ```
- **Deneyim:** Sahneyi temizlemek, önceki işlemlerden kalan gereksiz nesneleri kaldırarak test sürecini basitleştirdi. Kamera ve ışığı eklemek, render ile sonuçları görselleştirmek için gerekliydi.

## 3. BMesh ve Edit Mode Deneyleri

Aşağıda, `bmesh` modülünün ve Edit Mode'un çeşitli işlevlerini test etme sürecim ve bu süreçteki deneyimlerim yer alıyor. Her deney için amaç, kullanılan kod ve öğrenimler açıklanmıştır.

### 3.1 Farklı Bir Boyuta Geçiş (Edit Mode)
- **Amaç:** Object Mode ve Edit Mode arasındaki temel farkı anlamak ve aralarında programatik olarak nasıl geçiş yapılacağını öğrenmek.
- **Hipotez:** Edit Mode'a geçildiğinde, nesnenin kendisi yerine onu oluşturan bileşenlerin seçilebilir hale geleceğini tahmin ediyorum.
- **Kod:**
  ```python
  import bpy
  # Clear the scene
  bpy.ops.object.select_all(action='SELECT')
  bpy.ops.object.delete(use_global=False)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Select the cube
  bpy.context.view_layer.objects.active = bpy.data.objects['Cube']
  bpy.ops.object.select_all(action='DESELECT')
  bpy.data.objects['Cube'].select_set(True)
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Print current mode to console for verification
  print('Current mode after switching to Edit:', bpy.context.mode)
  # Switch back to Object Mode
  bpy.ops.object.mode_set(mode='OBJECT')
  # Print current mode to console for verification
  print('Current mode after switching back to Object:', bpy.context.mode)
  ```
- **Gözlem ve Sonuç:** `bpy.ops.object.mode_set(mode='EDIT')` komutu ile Edit Mode'a geçiş yaptım ve konsol çıktısı `EDIT_MESH` olarak doğrulandı, bu da nesnenin bileşenlerinin (vertex, edge, face) seçilebilir hale geldiğini gösteriyor. Ardından `mode='OBJECT'` parametresi ile Object Mode'a geri döndüm ve konsol çıktısı `OBJECT` olarak doğrulandı. Edit Mode'dayken nesnenin bileşenlerini manipüle etme imkanı olduğunu anladım, ancak şu anki ekran görüntüsü Object Mode'da alındığı için görsel farkı tam olarak belgeleyemedim. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da mevcut durumu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, Object Mode ve Edit Mode arasındaki temel farkı anlamama yardımcı oldu. Edit Mode, nesnenin detaylı bileşenlerini düzenlemek için kullanılıyor ve bu moda programatik olarak geçiş yapmak oldukça basit. Gelecekteki deneylerde, Edit Mode'da nesne bileşenlerini nasıl seçeceğimi ve manipüle edeceğimi test etmeyi planlıyorum. Ayrıca, Edit Mode'dayken alınmış bir ekran görüntüsü ile görsel farkları daha iyi belgelemeyi düşünüyorum.

### 3.2 Mikroskobu Ayarlamak (BMesh'e Giriş)
- **Amaç:** Aktif bir nesnenin mesh verisine `bmesh` üzerinden nasıl erişileceğini öğrenmek.
- **Hipotez:** `bmesh.from_edit_mesh()` fonksiyonu ile aktif nesnenin mesh verisinden bir BMesh nesnesi oluşturabileceğimi ve bu nesne üzerinden vertex, edge ve face sayılarını okuyabileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import bmesh
  # Clear the scene
  bpy.ops.object.select_all(action='SELECT')
  bpy.ops.object.delete(use_global=False)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Select the cube
  bpy.context.view_layer.objects.active = bpy.data.objects['Cube']
  bpy.ops.object.select_all(action='DESELECT')
  bpy.data.objects['Cube'].select_set(True)
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Create a BMesh object from the active mesh
  bm = bmesh.from_edit_mesh(bpy.context.active_object.data)
  # Print the counts of vertices, edges, and faces
  print('Number of vertices:', len(bm.verts))
  print('Number of edges:', len(bm.edges))
  print('Number of faces:', len(bm.faces))
  ```
- **Gözlem ve Sonuç:** `bmesh.from_edit_mesh()` fonksiyonu ile aktif nesnenin mesh verisinden bir BMesh nesnesi oluşturdum. Konsol çıktılarından, küp nesnesinin 8 vertex, 12 edge ve 6 face içerdiğini doğruladım. Bu, BMesh nesnesinin mesh verisine başarılı bir şekilde eriştiğini gösteriyor. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da Edit Mode'daki mevcut durumu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, `bmesh` modülünün mesh verilerine düşük seviyede erişim sağlamak için nasıl kullanılabileceğini anlamama yardımcı oldu. BMesh nesnesi üzerinden nesnenin geometrik bileşenlerine erişmek, detaylı modelleme işlemleri için temel bir adımdır. Gelecekteki deneylerde, bu bileşenleri nasıl seçeceğimi ve manipüle edeceğimi test etmeyi planlıyorum.

### 3.3 İlk Dokunuş (Bileşen Seçimi)
- **Amaç:** BMesh içindeki belirli bir geometri parçasını (örneğin bir yüzeyi) nasıl seçeceğini öğrenmek.
- **Hipotez:** BMesh nesnesi üzerinden bir yüzeyi indeksleme ile seçip `select` özelliğini `True` yaparak, bu yüzeyin Edit Mode'da görsel olarak seçili hale geleceğini düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import bmesh
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Ensure the cube is the active object
  cube = bpy.data.objects.get('Cube')
  if cube:
      bpy.context.view_layer.objects.active = cube
      cube.select_set(True)
      print('Cube selected as active object')
  else:
      print('Cube not found')
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Create a BMesh object from the active mesh
  bm = bmesh.from_edit_mesh(bpy.context.active_object.data)
  # Ensure lookup tables are updated before accessing elements
  bm.verts.ensure_lookup_table()
  bm.edges.ensure_lookup_table()
  bm.faces.ensure_lookup_table()
  # Deselect all components first
  for v in bm.verts:
      v.select = False
  for e in bm.edges:
      e.select = False
  for f in bm.faces:
      f.select = False
  # Select the first face
  if bm.faces:
      face = bm.faces[0]
      face.select = True
      print('Selected face index 0')
  else:
      print('No faces found')
  # Update the mesh to reflect changes in the viewport
  bmesh.update_edit_mesh(bpy.context.active_object.data)
  print('Mesh updated with selection')
  ```
- **Gözlem ve Sonuç:** BMesh nesnesi üzerinden ilk yüzeyi (indeks 0) seçtim ve `select` özelliğini `True` yaparak bu yüzeyi seçili hale getirdim. Konsol çıktısı, yüzeyin seçildiğini doğruladı. Ayrıca, tüm diğer bileşenleri (vertex ve edge) önceden deseçerek sadece hedef yüzeyin seçili olduğundan emin oldum. `bmesh.update_edit_mesh()` fonksiyonu ile yapılan değişiklikleri mesh'e geri yazdım, böylece seçim viewportta görsel olarak yansıdı. Ancak, ilk denemelerde "outdated internal index table" hatası aldım. Kullanıcı tarafından verilen ipucuna dayanarak, `ensure_lookup_table()` fonksiyonunu kullanarak bu hatayı çözdüm. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da seçili yüzeyin Edit Mode'daki durumunu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, BMesh üzerinden belirli geometrik bileşenleri seçmenin mümkün olduğunu ve bu seçimlerin viewportta görsel olarak yansıtılabileceğini anlamama yardımcı oldu. `ensure_lookup_table()` fonksiyonunun önemi, BMesh ile çalışırken sıkça karşılaşılan bir hatayı çözmek için kritik. Gelecekteki deneylerde, farklı bileşenleri (örneğin, vertex veya edge) seçmeyi ve bu seçimlerle manipülasyonlar yapmayı test etmeyi planlıyorum.
- **Hata Analizi:** İlk denemelerde "outdated internal index table" hatası aldım. Bu hata, BMesh nesnesinin iç indeks tablolarının güncel olmaması nedeniyle ortaya çıkıyor. `bm.verts.ensure_lookup_table()`, `bm.edges.ensure_lookup_table()` ve `bm.faces.ensure_lookup_table()` fonksiyonlarını kullanarak bu sorunu çözdüm. Bu, BMesh ile çalışırken dikkat edilmesi gereken önemli bir detay.

### 3.4 Maddeyi Genişletmek (Extrude)
- **Amaç:** Seçili bir yüzeyden yeni geometri oluşturmanın temelini, yani "extrude" işlemini öğrenmek.
- **Hipotez:** `bmesh.ops.extrude_face_region()` operatörü ile seçili bir yüzeyi ekstrüde ederek yeni geometri oluşturabileceğimi ve bu yeni geometriyi Z ekseninde taşıyabileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import bmesh
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Ensure the cube is the active object
  cube = bpy.data.objects.get('Cube')
  if cube:
      bpy.context.view_layer.objects.active = cube
      cube.select_set(True)
      print('Cube selected as active object')
  else:
      print('Cube not found')
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Create a BMesh object from the active mesh
  bm = bmesh.from_edit_mesh(bpy.context.active_object.data)
  # Ensure lookup tables are updated before accessing elements
  bm.verts.ensure_lookup_table()
  bm.edges.ensure_lookup_table()
  bm.faces.ensure_lookup_table()
  # Deselect all components first
  for v in bm.verts:
      v.select = False
  for e in bm.edges:
      e.select = False
  for f in bm.faces:
      f.select = False
  # Select the first face
  if bm.faces:
      face = bm.faces[0]
      face.select = True
      print('Selected face index 0')
  else:
      print('No faces found')
  # Extrude the selected face
  ret = bmesh.ops.extrude_face_region(bm, geom=[face], use_keep_orig=True)
  # Translate the new geometry along Z-axis
  new_geom = [ele for ele in ret['geom'] if isinstance(ele, bmesh.types.BMVert)]
  for vert in new_geom:
      vert.co.z += 1.0
  print('Extruded face and translated new geometry along Z-axis')
  # Update the mesh to reflect changes in the viewport
  bmesh.update_edit_mesh(bpy.context.active_object.data)
  print('Mesh updated with extrusion')
  ```
- **Gözlem ve Sonuç:** `bmesh.ops.extrude_face_region()` operatörü ile seçili yüzeyi ekstrüde ederek yeni geometri oluşturdum. `use_keep_orig=True` parametresi ile orijinal yüzeyin korunmasını sağladım. Ardından, döndürülen `ret['geom']` listesinden yeni oluşturulan vertex'leri filtreleyerek Z ekseninde 1 birim yukarı taşıdım. Konsol çıktısı, yüzeyin seçildiğini ve ekstrüzyon işleminin gerçekleştirildiğini doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da ekstrüde edilmiş geometrinin Edit Mode'daki durumunu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, ekstrüzyon işleminin yeni geometri oluşturmak için güçlü bir yöntem olduğunu anlamama yardımcı oldu. Seçili bir yüzeyden yeni bir hacim yaratmak, modelleme sürecinde temel bir adımdır. Gelecekteki deneylerde, farklı ekstrüzyon parametrelerini test etmeyi ve yeni geometriyi farklı yönlerde taşımayı planlıyorum.

### 3.5 Detaylandırmak (Subdivide)
- **Amaç:** Seçili bir yüzeye daha fazla detay eklemek için "subdivide" işlemini öğrenmek.
- **Hipotez:** `bmesh.ops.subdivide_edges()` operatörü ile seçili yüzeyin kenarlarını bölerek yeni geometri oluşturabileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import bmesh
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Ensure the cube is the active object
  cube = bpy.data.objects.get('Cube')
  if cube:
      bpy.context.view_layer.objects.active = cube
      cube.select_set(True)
      print('Cube selected as active object')
  else:
      print('Cube not found')
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Create a BMesh object from the active mesh
  bm = bmesh.from_edit_mesh(bpy.context.active_object.data)
  # Ensure lookup tables are updated before accessing elements
  bm.verts.ensure_lookup_table()
  bm.edges.ensure_lookup_table()
  bm.faces.ensure_lookup_table()
  # Deselect all components first
  for v in bm.verts:
      v.select = False
  for e in bm.edges:
      e.select = False
  for f in bm.faces:
      f.select = False
  # Select the first face
  if bm.faces:
      face = bm.faces[0]
      face.select = True
      print('Selected face index 0')
  else:
      print('No faces found')
  # Subdivide the selected face
  bmesh.ops.subdivide_edges(bm, edges=[e for e in bm.edges if e.select], cuts=1, use_grid_fill=True)
  print('Subdivided selected face')
  # Update the mesh to reflect changes in the viewport
  bmesh.update_edit_mesh(bpy.context.active_object.data)
  print('Mesh updated with subdivision')
  ```
- **Gözlem ve Sonuç:** `bmesh.ops.subdivide_edges()` operatörü ile seçili yüzeyin kenarlarını bir kez bölerek yeni geometri oluşturdum. `use_grid_fill=True` parametresi ile bölünen kenarların bir grid oluşturmasını sağladım, böylece yüzeyin içinde yeni yüzeyler oluştu. Konsol çıktısı, yüzeyin seçildiğini ve bölme işleminin gerçekleştirildiğini doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da bölünmüş yüzeyin Edit Mode'daki durumunu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, subdivide işleminin bir yüzeye detay eklemek için etkili bir yöntem olduğunu anlamama yardımcı oldu. Yüzeyin kenarlarını bölmek, daha karmaşık modeller oluşturmak için temel bir adımdır. Gelecekteki deneylerde, farklı subdivide parametrelerini test etmeyi ve birden fazla bölme işlemi yapmayı planlıyorum.

### 3.6 Yok Etmek (Delete)
- **Amaç:** Seçili bir yüzeyi mesh'ten kaldırmak için "delete" işlemini öğrenmek.
- **Hipotez:** `bmesh.ops.delete()` operatörü ile seçili bir yüzeyi silerek mesh yapısını değiştirebileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import bmesh
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a cube for testing
  bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 0))
  # Ensure the cube is the active object
  cube = bpy.data.objects.get('Cube')
  if cube:
      bpy.context.view_layer.objects.active = cube
      cube.select_set(True)
      print('Cube selected as active object')
  else:
      print('Cube not found')
  # Switch to Edit Mode
  bpy.ops.object.mode_set(mode='EDIT')
  # Create a BMesh object from the active mesh
  bm = bmesh.from_edit_mesh(bpy.context.active_object.data)
  # Ensure lookup tables are updated before accessing elements
  bm.verts.ensure_lookup_table()
  bm.edges.ensure_lookup_table()
  bm.faces.ensure_lookup_table()
  # Deselect all components first
  for v in bm.verts:
      v.select = False
  for e in bm.edges:
      e.select = False
  for f in bm.faces:
      f.select = False
  # Select the first face
  if bm.faces:
      face = bm.faces[0]
      face.select = True
      print('Selected face index 0')
  else:
      print('No faces found')
  # Delete the selected face
  bmesh.ops.delete(bm, geom=[face], context='FACES')
  print('Deleted selected face')
  # Update the mesh to reflect changes in the viewport
  bmesh.update_edit_mesh(bpy.context.active_object.data)
  print('Mesh updated after deletion')
  ```
- **Gözlem ve Sonuç:** `bmesh.ops.delete()` operatörü ile seçili yüzeyi mesh'ten sildim. `context='FACES'` parametresi ile sadece yüzeyin silinmesini sağladım, böylece ilişkili vertex ve edge'ler korunmuş olabilir. Konsol çıktısı, yüzeyin seçildiğini ve silme işleminin gerçekleştirildiğini doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da silinmiş yüzeyin Edit Mode'daki durumunu görselleştirmeme yardımcı oldu.
- **Öğrenim ve Çıkarım:** Bu deney, delete işleminin mesh yapısını değiştirmek için etkili bir yöntem olduğunu anlamama yardımcı oldu. Bir yüzeyi silmek, modelin topolojisini değiştirmek için temel bir adımdır. Gelecekteki deneylerde, farklı delete parametrelerini test etmeyi ve farklı bileşenleri silmeyi planlıyorum.

## 4. Genel Deneyim ve Öğrenimler

Bu bölüm, tüm deneyler tamamlandıktan sonra genel öğrenimlerimi ve `bmesh` ile Edit Mode'un kullanımına dair kapsamlı değerlendirmelerimi içerecek.

## 5. Sonuç ve Öneriler

Bu bölüm, `bmesh` ve Edit Mode ile çalışmaya yeni başlayanlar için öneriler ve sonuçlarımı özetleyecek. İlerledikçe, daha fazla deney gerçekleştirilip bu döküman genişletilebilir.
