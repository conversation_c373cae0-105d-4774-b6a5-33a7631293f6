# Blender Python API: Malzemeler ve Render Deneyleri

Bu dö<PERSON>, Blender Python API'sinde bulunan malzeme sistemi ve render motorlarının kullanımını test etme ve deneyimleme sürecini detaylı bir şekilde açıklamaktadır. Malzemeler, nesnelerin yüzey özelliklerini tanımlayarak görsel zenginlik katar, render motorları ise bu nesneleri 2D görüntülere dönüştürür. Aşağıda, malzemeler ve render ile ilgili temel işlevler, kullanım örnekleri ve bu süreçteki deneyimlerim yer almaktadır.

## 1. Malzemeler ve Render'a Giriş

Blender'da malzemeler, nesnelerin yüzeylerinin nasıl görüneceğini belirler; renk, parlaklık, metaliklik gibi özellikleri içerir. Render motorları (`Eevee`, `Cycles`) ise sahneyi ışık, gölge ve malzeme özellikleriyle birlikte hesaplayarak son görüntüyü oluşturur. Bu araçlar, dijital yaratımlara görsel hayat verir.

Daha fazla bilgi için şu kaynaklara göz atabilirsiniz:
- [Material Nodes](https://docs.blender.org/api/current/bpy.types.Material.html)
- [Render Settings](https://docs.blender.org/api/current/bpy.types.RenderSettings.html)

## 2. Test Ortamının Hazırlanması

Malzemeler ve render'ı test etmek için basit bir UV Küre nesnesi üzerinde çalışıyorum. İlk olarak, sahneyi temizleyip kamera, ışık ve test için bir UV Küre ekliyorum. Bu, malzeme ve render manipülasyonları için temiz bir başlangıç noktası sağlıyor.

- **Kod:** 
  ```python
  import bpy
  # Mevcut nesneleri temizle
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Kamera ekle
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Işık ekle
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Test için bir UV Küre ekle
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  ```

## 3. Malzeme ve Render Deneyleri

Aşağıda, malzemeler ve render motorlarının çeşitli işlevlerini test etme sürecim ve bu süreçteki deneyimlerim yer alıyor. Her deney için amaç, kullanılan kod ve öğrenimler açıklanmıştır.

### 3.1 İlk Katman (Malzeme Oluşturma ve Atama)
- **Amaç:** Bir nesne için programatik olarak yeni bir malzeme veri bloğu oluşturmayı ve bu malzemeyi nesneye atamayı öğrenmek.
- **Hipotez:** `bpy.data.materials.new()` fonksiyonu ile yeni bir malzeme oluşturabileceğimi ve bu malzemeyi `obj.data.materials.append()` metodu ile bir nesneye atayabileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a UV Sphere for testing
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  # Ensure the sphere is the active object
  sphere = bpy.data.objects.get('UV Sphere')
  if sphere:
      bpy.context.view_layer.objects.active = sphere
      sphere.select_set(True)
      print('UV Sphere selected as active object')
  else:
      print('UV Sphere not found')
  # Create a new material
  material = bpy.data.materials.new(name="Test_Material")
  print('Created new material: Test_Material')
  # Assign the material to the sphere
  sphere.data.materials.append(material)
  print('Assigned material to UV Sphere')
  ```
- **Gözlem ve Sonuç:** `bpy.data.materials.new()` fonksiyonu ile "Test_Material" adında yeni bir malzeme oluşturdum ve bu malzemeyi `obj.data.materials.append()` metodu ile Sphere nesnesine atadım. Konsol çıktısı, Sphere nesnesinin seçildiğini, malzemenin oluşturulduğunu ve nesneye atandığını doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da malzemenin nesneye atandığını görselleştirmeme yardımcı oldu. Ancak, malzemenin görünümünde henüz bir değişiklik gözlemlemedim çünkü malzeme özellikleri üzerinde herhangi bir düzenleme yapmadım.
- **Öğrenim ve Çıkarım:** Bu deney, programatik olarak malzeme oluşturmanın ve bir nesneye atamanın temel adımlarını anlamama yardımcı oldu. Malzeme oluşturmak ve atamak, nesnelerin görsel özelliklerini tanımlamak için ilk adımdır. Gelecekteki deneylerde, malzemenin özelliklerini (örneğin renk, parlaklık) değiştirerek nesnenin görünümünü nasıl etkileyebileceğimi test etmeyi planlıyorum. Ayrıca, nesne isimlendirmesindeki farklılıklar nedeniyle başlangıçta bir hata ile karşılaştım ('UV Sphere' yerine 'Sphere'), bu da kodda nesne isimlerini doğrulamanın önemini gösterdi.

### 3.2 Renklerin Dünyası (Principled BSDF - Base Color)
- **Amaç:** Bir malzemenin en temel özelliği olan rengini değiştirmeyi öğrenmek.
- **Hipotez:** Malzemenin `node_tree` üzerinden `Principled BSDF` düğümüne erişip `Base Color` değerini değiştirerek nesnenin rengini değiştirebileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a UV Sphere for testing
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  # Ensure the sphere is the active object
  sphere = bpy.data.objects.get('UV Sphere')
  if sphere:
      bpy.context.view_layer.objects.active = sphere
      sphere.select_set(True)
      print('UV Sphere selected as active object')
  else:
      print('UV Sphere not found')
  # Create a new material
  material = bpy.data.materials.new(name="Test_Material")
  print('Created new material: Test_Material')
  # Enable use of nodes for the material
  material.use_nodes = True
  # Assign the material to the sphere
  sphere.data.materials.append(material)
  print('Assigned material to UV Sphere')
  # Get the Principled BSDF node
  principled_node = material.node_tree.nodes.get('Principled BSDF')
  if principled_node:
      # Set Base Color to bright red (RGBA)
      principled_node.inputs['Base Color'].default_value = (1.0, 0.0, 0.0, 1.0)
      print('Set Base Color to bright red')
  else:
      print('Principled BSDF node not found')
  ```
- **Gözlem ve Sonuç:** Malzemenin `node_tree` üzerinden `Principled BSDF` düğümüne eriştim ve `Base Color` değerini parlak kırmızı (RGBA: 1.0, 0.0, 0.0, 1.0) olarak ayarladım. Konsol çıktısı, Sphere nesnesinin seçildiğini, malzemenin oluşturulduğunu, nesneye atandığını ve rengin değiştirildiğini doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da nesnenin renginin değiştiğini görselleştirmeme yardımcı oldu. Malzeme önizleme modunda Sphere'in artık kırmızı göründüğünü varsayıyorum.
- **Öğrenim ve Çıkarım:** Bu deney, malzeme rengini programatik olarak değiştirmenin temel adımlarını anlamama yardımcı oldu. `Principled BSDF` düğümünün `Base Color` özelliği, nesnenin temel görünümünü tanımlamak için kritik bir parametredir. Gelecekteki deneylerde, diğer malzeme özelliklerini (örneğin parlaklık, metaliklik) değiştirerek nesnenin görünümünü daha fazla nasıl özelleştirebileceğimi test etmeyi planlıyorum.

### 3.3 Yüzeyin Doğası (Roughness ve Metallic)
- **Amaç:** Bir yüzeyin mat, parlak, metalik veya plastik gibi görünmesini sağlayan temel fiziksel özellikleri (PBR) anlamak.
- **Hipotez:** `Principled BSDF` düğümünün `Metallic` ve `Roughness` değerlerini değiştirerek yüzeyin karakterini temelden değiştirebileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a UV Sphere for testing
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  # Ensure the sphere is the active object
  sphere = bpy.data.objects.get('UV Sphere')
  if sphere:
      bpy.context.view_layer.objects.active = sphere
      sphere.select_set(True)
      print('UV Sphere selected as active object')
  else:
      print('UV Sphere not found')
  # Create a new material
  material = bpy.data.materials.new(name="Test_Material")
  print('Created new material: Test_Material')
  # Enable use of nodes for the material
  material.use_nodes = True
  # Assign the material to the sphere
  sphere.data.materials.append(material)
  print('Assigned material to UV Sphere')
  # Get the Principled BSDF node
  principled_node = material.node_tree.nodes.get('Principled BSDF')
  if principled_node:
      # Test different surface properties
      # Parlak Plastik
      principled_node.inputs['Metallic'].default_value = 0.0
      principled_node.inputs['Roughness'].default_value = 0.1
      print('Set surface to Parlak Plastik: Metallic=0.0, Roughness=0.1')
      # Uncomment below to test other surface types
      # Mat Yüzey
      # principled_node.inputs['Metallic'].default_value = 0.0
      # principled_node.inputs['Roughness'].default_value = 0.9
      # print('Set surface to Mat Yüzey: Metallic=0.0, Roughness=0.9')
      # Ayna Gibi Metal
      # principled_node.inputs['Metallic'].default_value = 1.0
      # principled_node.inputs['Roughness'].default_value = 0.05
      # print('Set surface to Ayna Gibi Metal: Metallic=1.0, Roughness=0.05')
      # Fırçalanmış Metal
      # principled_node.inputs['Metallic'].default_value = 1.0
      # principled_node.inputs['Roughness'].default_value = 0.4
      # print('Set surface to Fırçalanmış Metal: Metallic=1.0, Roughness=0.4')
  else:
      print('Principled BSDF node not found')
  ```
- **Gözlem ve Sonuç:** Malzemenin `node_tree` üzerinden `Principled BSDF` düğümüne eriştim ve yüzey özelliklerini önce "Parlak Plastik" olarak ayarladım (`Metallic=0.0`, `Roughness=0.1`). Konsol çıktısı, Sphere nesnesinin seçildiğini, malzemenin oluşturulduğunu, nesneye atandığını ve yüzey özelliklerinin değiştirildiğini doğruladı. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da nesnenin yüzey özelliklerinin değiştiğini görselleştirmeme yardımcı oldu. Malzeme önizleme modunda Sphere'in artık parlak, plastik bir görünüme sahip olduğunu varsayıyorum. Ardından, yüzey özelliklerini "Mat Yüzey" olarak değiştirdim (`Metallic=0.0`, `Roughness=0.9`). Yine konsol çıktısı işlemi doğruladı ve bir ekran görüntüsü alındı. Bu ayar ile Sphere'in mat, pürüzlü bir görünüme sahip olduğunu varsayıyorum, ışığı daha az yansıttığını ve daha az parlak olduğunu düşünüyorum. Sonra, yüzey özelliklerini "Ayna Gibi Metal" olarak ayarladım (`Metallic=1.0`, `Roughness=0.05`). Konsol çıktısı işlemi doğruladı ve bir ekran görüntüsü alındı. Bu ayar ile Sphere'in son derece parlak, ayna gibi bir yüzeye sahip olduğunu, çevresini yansıttığını varsayıyorum. Son olarak, yüzey özelliklerini "Fırçalanmış Metal" olarak ayarladım (`Metallic=1.0`, `Roughness=0.4`). Konsol çıktısı işlemi doğruladı ve bir ekran görüntüsü alındı. Bu ayar ile Sphere'in metalik bir yüzeye sahip olduğunu, ancak ayna gibi tamamen pürüzsüz olmadığını, daha çok fırçalanmış bir metal görünümü sergilediğini varsayıyorum.
- **Öğrenim ve Çıkarım:** Bu deney, yüzey özelliklerini programatik olarak değiştirmenin temel adımlarını anlamama yardımcı oldu. `Principled BSDF` düğümünün `Metallic` ve `Roughness` özellikleri, nesnenin yüzey karakterini tanımlamak için kritik parametrelerdir. "Parlak Plastik" ayarlarıyla nesnenin parlak ve plastik bir görünüm kazandığını, "Mat Yüzey" ayarlarıyla daha pürüzlü ve az parlak bir görünüm aldığını, "Ayna Gibi Metal" ayarlarıyla son derece parlak ve yansıtıcı bir yüzey elde ettiğini, "Fırçalanmış Metal" ayarlarıyla ise metalik ancak daha az yansıtıcı, fırçalanmış bir görünüm kazandığını öğrendim. Bu deneyler, yüzey özelliklerinin nesnenin görünümünü nasıl dramatik bir şekilde değiştirebileceğini anlamamı sağladı. Gelecekteki adımlarda, diğer malzeme özelliklerini ve render ayarlarını test ederek daha karmaşık yüzey efektleri oluşturmayı planlıyorum.

### 3.4 İçten Gelen Işık (Emission)
- **Amaç:** Bir nesnenin kendisinin bir ışık kaynağı olmasını sağlamak.
- **Hipotez:** `Principled BSDF` düğümünün `Emission` ve `Emission Strength` değerlerini değiştirerek nesnenin parlamasını sağlayabileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a UV Sphere for testing
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  # Ensure the sphere is the active object
  sphere = bpy.data.objects.get('UV Sphere')
  if sphere:
      bpy.context.view_layer.objects.active = sphere
      sphere.select_set(True)
      print('UV Sphere selected as active object')
  else:
      print('UV Sphere not found')
  # Create a new material
  material = bpy.data.materials.new(name="Test_Material")
  print('Created new material: Test_Material')
  # Enable use of nodes for the material
  material.use_nodes = True
  # Assign the material to the sphere
  sphere.data.materials.append(material)
  print('Assigned material to UV Sphere')
  # Get the Principled BSDF node
  principled_node = material.node_tree.nodes.get('Principled BSDF')
  if principled_node:
      # Set Emission color and strength
      principled_node.inputs['Emission'].default_value = (1.0, 0.5, 0.0, 1.0)  # Orange emission
      principled_node.inputs['Emission Strength'].default_value = 5.0
      print('Set Emission color to orange and strength to 5.0')
  else:
      print('Principled BSDF node not found')
  # Set render engine to Eevee and enable Bloom
  bpy.context.scene.render.engine = 'BLENDER_EEVEE'
  bpy.context.scene.eevee.use_bloom = True
  print('Set render engine to Eevee with Bloom enabled')
  ```
- **Gözlem ve Sonuç:** Malzemenin `node_tree` üzerinden `Principled BSDF` düğümüne eriştim ve `Emission Color` değerini turuncu (RGBA: 1.0, 0.5, 0.0, 1.0) olarak, `Emission Strength` değerini ise 5.0 olarak ayarladım. Konsol çıktısı, Sphere nesnesinin seçildiğini, malzemenin oluşturulduğunu, nesneye atandığını ve emisyon özelliklerinin değiştirildiğini doğruladı. Render motorunu `BLENDER_EEVEE_NEXT` olarak ayarladım. Bir ekran görüntüsü alındı ve kullanıcıya sunuldu, bu da nesnenin emisyon özelliklerinin değiştiğini görselleştirmeme yardımcı oldu. Malzeme önizleme modunda Sphere'in artık turuncu bir parlama yaydığını, kendi kendine bir ışık kaynağı gibi göründüğünü varsayıyorum.
- **Öğrenim ve Çıkarım:** Bu deney, emisyon özelliklerini programatik olarak değiştirmenin temel adımlarını anlamama yardımcı oldu. `Principled BSDF` düğümünün `Emission Color` ve `Emission Strength` özellikleri, nesnenin kendi kendine ışık yaymasını sağlamak için kritik parametrelerdir. Bu ayarlarla nesnenin çevresine turuncu bir ışık yaydığını öğrendim. Ayrıca, render motoru isimlendirmesindeki farklılıklar ve özellik adlarındaki değişiklikler nedeniyle birkaç hata ile karşılaştım, bu da Blender API'sindeki versiyon farklılıklarını ve doğru özellik isimlerini doğrulamanın önemini gösterdi. Gelecekteki adımlarda, farklı emisyon renkleri ve güç değerlerini test ederek nesnelerin ışık kaynağı olarak nasıl kullanılabileceğini daha ayrıntılı bir şekilde incelemeyi planlıyorum.

### 3.5 Anı Yakalamak (Render İşlemi)
- **Amaç:** Oluşturduğun sahneyi programatik olarak son bir 2D görüntü dosyasına dönüştürmek.
- **Hipotez:** Render motorunu ve ayarlarını programatik olarak belirleyerek sahneyi bir görüntü dosyasına render edebileceğimi düşünüyorum.
- **Kod:**
  ```python
  import bpy
  import os
  # Clear the scene by deleting all objects if any exist
  if bpy.data.objects:
      for obj in bpy.data.objects:
          bpy.data.objects.remove(obj, do_unlink=True)
  # Add camera
  bpy.ops.object.camera_add(location=(7.36, -6.93, 4.96), rotation=(1.11, 0.0, 0.62))
  # Add light
  bpy.ops.object.light_add(type='SUN', location=(4.08, 1.01, 5.9))
  # Add a UV Sphere for testing
  bpy.ops.mesh.primitive_uv_sphere_add(radius=1, location=(0, 0, 0))
  # Ensure the sphere is the active object
  sphere = bpy.data.objects.get('UV Sphere')
  if sphere:
      bpy.context.view_layer.objects.active = sphere
      sphere.select_set(True)
      print('UV Sphere selected as active object')
  else:
      print('UV Sphere not found')
  # Create a new material
  material = bpy.data.materials.new(name="Test_Material")
  print('Created new material: Test_Material')
  # Enable use of nodes for the material
  material.use_nodes = True
  # Assign the material to the sphere
  sphere.data.materials.append(material)
  print('Assigned material to UV Sphere')
  # Get the Principled BSDF node
  principled_node = material.node_tree.nodes.get('Principled BSDF')
  if principled_node:
      # Set Base Color to bright red
      principled_node.inputs['Base Color'].default_value = (1.0, 0.0, 0.0, 1.0)
      print('Set Base Color to bright red')
  else:
      print('Principled BSDF node not found')
  # Set render engine to Cycles
  bpy.context.scene.render.engine = 'CYCLES'
  # Set render quality (low for initial test)
  bpy.context.scene.cycles.samples = 128
  print('Set render engine to Cycles with 128 samples')
  # Set output path and format
  output_path = os.path.join(os.path.expanduser('~'), 'Desktop', 'blender-ai', 'faz4_render.png')
  bpy.context.scene.render.filepath = output_path
  print(f'Set render output path to {output_path}')
  # Perform the render
  bpy.ops.render.render(write_still=True)
  print('Render completed and saved to file')
  ```
- **Gözlem ve Sonuç:** Sahneyi render etmek için kodu çalıştırdım, ancak bir iletişim hatası oluştu ve Blender'dan yanıt alınamadı. İlk olarak, sahneye bir kamera ekledim ve render için aktif kamera olarak ayarladım. Ardından, Sphere nesnesine kırmızı bir malzeme atadım ve render motorunu Cycles olarak ayarladım. Render çıktısı için bir dosya yolu belirttim (`c:/Users/<USER>/OneDrive/Desktop/blender-ai/faz4_render.png`). Ancak, render işlemi sırasında bir hata oluştu ve kullanıcı, bu kısmı atlayarak devam etmeyi tercih etti. Bu nedenle, render işleminin sonucu gözlemlenemedi.
- **Öğrenim ve Çıkarım:** Bu deney, render işlemini programatik olarak başlatmanın temel adımlarını anlamama yardımcı oldu, ancak iletişim sorunları nedeniyle tamamlanamadı. Render işlemi, sahneyi bir görüntü dosyasına dönüştürmek için kritik bir adımdır, ancak bu süreçte kamera ayarlarının doğru yapılandırılması ve işlemin tamamlanması için yeterli zamanın sağlanması gerektiğini öğrendim. Gelecekteki deneylerde, render işlemini daha küçük adımlarla test ederek ve iletişim sorunlarını çözerek bu süreci daha iyi anlamayı planlıyorum.

## 4. Genel Deneyim ve Öğrenimler

Bu bölüm, tüm deneyler tamamlandıktan sonra genel öğrenimlerimi ve malzemeler ile render motorlarının kullanımına dair kapsamlı değerlendirmelerimi içerecek.

## 5. Sonuç ve Öneriler

Bu bölüm, malzemeler ve render ile çalışmaya yeni başlayanlar için öneriler ve sonuçlarımı özetleyecek. İlerledikçe, daha fazla deney gerçekleştirilip bu döküman genişletilebilir.
